# EPS Processing Performance Improvements

## Overview

This document outlines the performance improvements made to the `setEPS` functionality, implementing batch processing to optimize database operations and improve overall processing efficiency.

## Key Improvements

### 1. Configurable Batch Processing

- **Batch Size Configuration**: Added `epsBatchSize` setting to the `agendaSettings` collection
- **Default Value**: 10 tickers per batch (configurable via API)
- **Range**: 1-50 tickers per batch
- **API Endpoint**: `PUT /api/agenda/settings` with `{"epsBatchSize": 10}`

### 2. Optimized Database Operations

- **Batch-level Processing**: Tickers are processed in configurable batches to optimize database connections
- **Limited Concurrency**: Maximum 2 concurrent batches to prevent database overload
- **Sequential Within Batch**: Database operations within each batch are processed sequentially for data consistency
- **Error Isolation**: Errors in individual tickers don't stop the entire batch processing

### 3. Enhanced Progress Tracking

- **Job Progress Integration**: Full integration with the existing job progress tracking system
- **Batch-level Logging**: Detailed logging for each batch with timing information
- **Error Handling**: Comprehensive error logging with ticker-specific details

## Technical Implementation

### Batch Processing Functions

```typescript
// Batch creation and processing functions
function createBatchesForEPS(tickers: ListOfTickers[], batchSize: number): ListOfTickers[][]
async function processBatchForEPS(batch: ListOfTickers[], batchIndex: number, totalBatches: number): Promise<void>
async function processBatchesSequentiallyForEPS(batches: ListOfTickers[][], historyId?: string): Promise<void>
async function processBatchesConcurrentlyForEPS(batches: ListOfTickers[][], maxConcurrency: number, historyId?: string): Promise<void>
```

### Updated Configuration

```typescript
// New agenda setting
epsBatchSize: 10 // Default batch size for EPS processing

// New helper function
export const getEpsBatchSize = async (): Promise<number>
```

### Processing Logic

1. **Batch Creation**: Tickers are divided into batches based on the configured batch size
2. **Concurrency Decision**: System automatically chooses between sequential and concurrent processing (max 2 concurrent batches)
3. **Database Operations**: For each ticker:
   - Fetch yearly and quarterly income statements
   - Fetch corresponding balance sheets
   - Calculate EPS for both yearly and quarterly data
   - Update income statement records with calculated EPS values
4. **Progress Updates**: Job progress is updated after each batch completion

## EPS Calculation Process

### Data Sources
- **Income Statement**: Net income, existing EPS values
- **Balance Sheet**: Common stock shares outstanding
- **Cash Flow**: Alternative net income source when income statement value is null

### Calculation Steps
1. **Yearly Processing**:
   - Fetch yearly income statements and balance sheets
   - Calculate current EPS: `net_income / common_stock_shares_outstanding`
   - Calculate previous period EPS for comparison
   - Update `eps_diluted_current` and `eps_diluted_last_date` fields

2. **Quarterly Processing**:
   - Same process as yearly but for quarterly data
   - Maintains separate quarterly EPS calculations

3. **Fallback Logic**:
   - If net income is null or zero, fetch from cash flow statement
   - Ensures EPS calculation completeness

## Performance Benefits

### Before (Sequential Processing)
- **Database Connections**: One connection per ticker, processed sequentially
- **Error Handling**: Single ticker error could affect processing flow
- **Progress Tracking**: Updated after each individual ticker
- **Resource Usage**: Underutilized database connection pooling

### After (Batch Processing)
- **Database Connections**: Optimized connection usage with batch processing
- **Error Handling**: Isolated error handling per ticker, batch processing continues
- **Progress Tracking**: Efficient batch-level progress updates
- **Resource Usage**: Better database connection management and throughput

### Expected Performance Gains

| Dataset Size | Sequential Time | Batch Time (Est.) | Improvement |
|--------------|----------------|-------------------|-------------|
| 50 tickers   | ~10 minutes    | ~3-5 minutes      | 50-70%      |
| 100 tickers  | ~20 minutes    | ~6-8 minutes      | 60-70%      |
| 500 tickers  | ~100 minutes   | ~25-35 minutes    | 65-75%      |

*Note: Performance gains depend on database performance and data complexity*

## Configuration

### Setting EPS Batch Size

```bash
# Set EPS batch size to 15
curl -X PUT http://localhost:3000/api/agenda/settings \
  -H "Content-Type: application/json" \
  -d '{"epsBatchSize": 15}'

# Set multiple settings at once
curl -X PUT http://localhost:3000/api/agenda/settings \
  -H "Content-Type: application/json" \
  -d '{"epsBatchSize": 15, "splitsBatchSize": 10, "dividendsBatchSize": 10}'
```

### Recommended Batch Sizes

- **Small datasets (1-20 tickers)**: Batch size 5-8
- **Medium datasets (21-100 tickers)**: Batch size 8-12
- **Large datasets (100+ tickers)**: Batch size 10-15
- **Very large datasets (500+ tickers)**: Batch size 12-20

*Note: EPS processing is database-intensive, so smaller batch sizes are recommended compared to API-based operations*

## Database Considerations

### Connection Management
- **Limited Concurrency**: Maximum 2 concurrent batches to prevent database overload
- **Sequential Processing**: Within each batch, tickers are processed sequentially
- **Connection Pooling**: Leverages existing Sequelize connection pooling

### Query Optimization
- **Selective Attributes**: Only fetch required fields to reduce memory usage
- **Ordered Results**: Consistent ordering for reliable EPS calculations
- **Conditional Queries**: Use `Op.in` for efficient batch queries

## Error Handling

- **Ticker-level Errors**: Individual ticker failures are logged but don't stop batch processing
- **Batch-level Errors**: Batch failures are logged and processing continues with remaining batches
- **Progress Tracking**: Progress is updated even when errors occur to maintain accurate tracking
- **Data Integrity**: Database transactions ensure data consistency during EPS updates

## Testing

### Manual Testing

```bash
# Test EPS processing with specific tickers
curl -X POST http://localhost:3000/api/jobs/run \
  -H "Content-Type: application/json" \
  -d '{"jobName": "setEPS", "data": {"tickerCodes": ["AAPL.US", "MSFT.US"]}}'
```

### Monitoring

- **Logs**: Monitor batch processing logs for timing and error information
- **Progress**: Use the job progress API to track processing status
- **Database Performance**: Monitor database connection usage and query performance

## Future Enhancements

1. **Query Optimization**: Further optimize database queries for better performance
2. **Parallel EPS Calculations**: Implement parallel processing for yearly and quarterly calculations
3. **Caching**: Cache frequently accessed balance sheet and income statement data
4. **Incremental Processing**: Only process tickers with updated financial data
5. **Performance Metrics**: Add detailed performance metrics for database operations
6. **Connection Optimization**: Implement custom connection management for batch operations
