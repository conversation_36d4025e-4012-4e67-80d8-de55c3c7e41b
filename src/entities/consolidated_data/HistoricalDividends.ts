import {DataTypes, Model, Optional} from "sequelize";
import {sequelize as conn} from "../../config/database";
import {iHistoricalDividends} from "./iHistoricalDividends";

type HistoricalDividendsCreationAttributes = Optional<
    iHistoricalDividends,
    "id" | "created_at" | "updated_at" | "currency_symbol" | "declaration_date" | "record_date" | "payment_date" | "unadjusted_value" | "document_date" | "document_type_year_or_quarter" | "value"
>;

class HistoricalDividends extends Model<iHistoricalDividends, HistoricalDividendsCreationAttributes> {
    declare id?: number;
    declare ticker_internal_id: number;
    declare created_at?: string;
    declare updated_at?: string;
    declare document_date?: string;
    declare document_type_year_or_quarter?: string;
    declare currency_symbol?: string;
    declare declaration_date?: string;
    declare record_date?: string;
    declare payment_date?: string;
    declare value?: number;
    declare unadjusted_value?: number;
}

HistoricalDividends.init(
    {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        ticker_internal_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        created_at: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        updated_at: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        document_type_year_or_quarter: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        document_date: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        currency_symbol: {
            type: DataTypes.STRING(6),
            allowNull: true,
        },
        declaration_date: {
            type: DataTypes.DATEONLY,
            allowNull: true,
        },
        record_date: {
            type: DataTypes.DATEONLY,
            allowNull: true,
        },
        payment_date: {
            type: DataTypes.DATEONLY,
            allowNull: true,
        },
        value: {
            type: DataTypes.DECIMAL(18, 5),
            allowNull: true,
        },
        unadjusted_value: {
            type: DataTypes.DECIMAL(18, 5),
            allowNull: true,
        },
    },
    {
        timestamps: false,
        sequelize: conn,
        tableName: "historical_dividends",
        hooks: {
            beforeCreate: (record) => {
                record.dataValues.created_at = new Date().toISOString();
                record.dataValues.updated_at = new Date().toISOString();
            },
            beforeUpdate: (record) => {
                record.dataValues.updated_at = new Date().toISOString();
            },
        },
    },
);

export {HistoricalDividends};
