import {Model, Optional, DataTypes} from "sequelize";
import {sequelize as conn} from "../../config/database";
import {iHistoricalSplits} from "./iHistoricalSplits";

type HistoricalSplitsCreationAttributes = Optional<iHistoricalSplits, "id" | "created_at" | "updated_at">;

class HistoricalSplits extends Model<iHistoricalSplits, HistoricalSplitsCreationAttributes> {
    declare id?: number;
    declare ticker_internal_id: number;
    declare created_at?: Date;
    declare updated_at?: Date;
    declare document_date: Date;
    declare split?: number;
    declare insplit?: number;
}

HistoricalSplits.init(
    {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        ticker_internal_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        created_at: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        updated_at: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        split: {
            type: DataTypes.DECIMAL(18, 5),
            allowNull: false,
        },
        insplit: {
            type: DataTypes.DECIMAL(18, 5),
            allowNull: false,
        },
        document_date: {
            type: DataTypes.DATEONLY,
            allowNull: false,
        },
    },
    {
        timestamps: false,
        sequelize: conn,
        tableName: "historical_splits",
        hooks: {
            beforeCreate: (record, options) => {
                record.dataValues.created_at = new Date();
                record.dataValues.updated_at = new Date();
            },
            beforeUpdate: (record, options) => {
                record.dataValues.updated_at = new Date();
            },
        },
    },
);

export {HistoricalSplits};
